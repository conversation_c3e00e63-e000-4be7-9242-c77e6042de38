import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import '../../../../resources/resources.dart';

class RevampMarkDownBulletTextItem extends StatelessWidget {
  final String text;
  final bool isShowDashLine;

  const RevampMarkDownBulletTextItem({
    required this.text,
    this.isShowDashLine = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: Column(
        children: <Widget>[
          isShowDashLine
              ? Container(
                  color: evoColors.divider,
                  height: 1,
                )
              : const SizedBox.shrink(),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: Container(
                    alignment: Alignment.center,
                    child: Markdown(
                      shrinkWrap: true,
                      styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
                        p: evoTextStyles.bodyMedium(evoColors.textPassive).copyWith(height: 1.42),
                        strong:
                            evoTextStyles.h200(color: evoColors.textActive).copyWith(height: 1.42),
                      ),
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      data: text,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
