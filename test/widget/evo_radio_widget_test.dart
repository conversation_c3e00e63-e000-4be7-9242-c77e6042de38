// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/evo_radio_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/init_common_package.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColorV2();
    getItRegisterTextStyleV2();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoRadioWidget', () {
    testWidgets('should render correctly when not selected', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(title), findsOneWidget);
      expect(find.byType(EvoRadioWidget<String>), findsOneWidget);

      // Verify the radio button is not selected (no inner circle)
      final Container container = tester.widget<Container>(find.descendant(
        of: find.byType(EvoRadioWidget<String>),
        matching: find.byType(Container).first,
      ));
      expect(container.child, isNull);

      // Verify correct styling when not selected
      expect(
        (tester.widget(find.byType(Text)) as Text).style?.color,
        evoColorsV2.checkBoxRadioDisable,
      );
    });

    testWidgets('should render correctly when selected', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
              isSelected: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(title), findsOneWidget);

      // Verify the radio button is selected (has inner circle)
      expect(find.descendant(
        of: find.byType(EvoRadioWidget<String>),
        matching: find.byType(Container),
      ), findsNWidgets(2)); // Outer container and inner circle container

      // Verify correct styling when selected
      expect(
        (tester.widget(find.byType(Text)) as Text).style?.color,
        evoColorsV2.checkBoxRadioDefault,
      );
    });

    testWidgets('should trigger onChange callback when tapped and enabled', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';
      String? selectedValue;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
              onChange: (String val) {
                selectedValue = val;
              },
            ),
          ),
        ),
      );

      // Tap the radio widget
      await tester.tap(find.byType(EvoRadioWidget<String>));
      await tester.pump();

      // Assert
      expect(selectedValue, equals(value));
    });

    testWidgets('should not trigger onChange callback when tapped and disabled', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';
      String? selectedValue;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
              enable: false,
              onChange: (String val) {
                selectedValue = val;
              },
            ),
          ),
        ),
      );

      // Tap the radio widget
      await tester.tap(find.byType(EvoRadioWidget<String>));
      await tester.pump();

      // Assert
      expect(selectedValue, isNull);
    });

    testWidgets('should work with different generic types', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const int value = 42;
      int? selectedValue;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<int>(
              title: title,
              value: value,
              onChange: (int val) {
                selectedValue = val;
              },
            ),
          ),
        ),
      );

      // Tap the radio widget
      await tester.tap(find.byType(EvoRadioWidget<int>));
      await tester.pump();

      // Assert
      expect(selectedValue, equals(value));
    });

    testWidgets('should have proper visual appearance', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act - test selected state
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
              isSelected: true,
            ),
          ),
        ),
      );

      // Find the decoration of the outer container
      final Container outerContainer = tester.widget<Container>(find.descendant(
        of: find.byType(EvoRadioWidget<String>),
        matching: find.byType(Container).first,
      ));

      final BoxDecoration decoration = outerContainer.decoration as BoxDecoration;

      // Assert - verify border color for selected state
      expect(decoration.border?.top.color, equals(evoColorsV2.borderPrimary));
      expect(decoration.shape, equals(BoxShape.circle));

      // Act - test unselected state
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
            ),
          ),
        ),
      );

      // Find the decoration of the outer container
      final Container unselectedContainer = tester.widget<Container>(find.descendant(
        of: find.byType(EvoRadioWidget<String>),
        matching: find.byType(Container).first,
      ));

      final BoxDecoration unselectedDecoration = unselectedContainer.decoration as BoxDecoration;

      // Assert - verify border color for unselected state
      expect(unselectedDecoration.border?.top.color, equals(evoColorsV2.borderLine));
      expect(unselectedDecoration.color, equals(evoColorsV2.backgroundNeutralContainer));
    });

    testWidgets('should handle null onChange callback gracefully', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
            ),
          ),
        ),
      );

      // Tap the radio widget - should not throw any errors
      await tester.tap(find.byType(EvoRadioWidget<String>));
      await tester.pump();

      // Assert - no exception should be thrown
      expect(find.byType(EvoRadioWidget<String>), findsOneWidget);
    });

    testWidgets('should maintain proper spacing between radio and title', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
            ),
          ),
        ),
      );

      // Assert - verify SizedBox spacing
      final SizedBox sizedBox = tester.widget<SizedBox>(find.descendant(
        of: find.byType(EvoRadioWidget<String>),
        matching: find.byType(SizedBox),
      ));
      expect(sizedBox.width, equals(12));
    });

    testWidgets('should have correct radio button dimensions', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
              isSelected: true,
            ),
          ),
        ),
      );

      // Assert - verify radio button container dimensions
      final Container radioContainer = tester.widget<Container>(find.descendant(
        of: find.byType(EvoRadioWidget<String>),
        matching: find.byType(Container).first,
      ));

      // Check the width and height constraints from the Container
      expect(radioContainer.constraints?.minWidth, equals(20));
      expect(radioContainer.constraints?.minHeight, equals(20));
    });

    testWidgets('should use correct text style for title', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
              isSelected: true,
            ),
          ),
        ),
      );

      // Assert - verify text style
      final Text textWidget = tester.widget<Text>(find.text(title));
      expect(textWidget.style?.color, equals(evoColorsV2.checkBoxRadioDefault));
    });

    testWidgets('should handle disabled state visual styling correctly', (WidgetTester tester) async {
      // Arrange
      const String title = 'Disabled Radio';
      const String value = 'disabled_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
              enable: false,
            ),
          ),
        ),
      );

      // Assert - verify disabled styling
      final Text textWidget = tester.widget<Text>(find.text(title));
      expect(textWidget.style?.color, equals(evoColorsV2.checkBoxRadioDisable));
    });

    testWidgets('should work with complex object types', (WidgetTester tester) async {
      // Arrange
      const String title = 'Complex Object Radio';
      final Map<String, dynamic> complexValue = <String, dynamic>{'id': 1, 'name': 'test'};
      Map<String, dynamic>? selectedValue;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<Map<String, dynamic>>(
              title: title,
              value: complexValue,
              onChange: (Map<String, dynamic> val) {
                selectedValue = val;
              },
            ),
          ),
        ),
      );

      // Tap the radio widget
      await tester.tap(find.byType(EvoRadioWidget<Map<String, dynamic>>));
      await tester.pump();

      // Assert
      expect(selectedValue, equals(complexValue));
    });

    testWidgets('should have transparent splash and highlight colors', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
            ),
          ),
        ),
      );

      // Assert - verify InkWell properties
      final InkWell inkWell = tester.widget<InkWell>(find.descendant(
        of: find.byType(EvoRadioWidget<String>),
        matching: find.byType(InkWell),
      ));
      expect(inkWell.splashColor, equals(Colors.transparent));
      expect(inkWell.highlightColor, equals(Colors.transparent));
    });

    testWidgets('should have proper Row layout with correct mainAxisSize', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
            ),
          ),
        ),
      );

      // Assert - verify Row properties
      final Row row = tester.widget<Row>(find.descendant(
        of: find.byType(EvoRadioWidget<String>),
        matching: find.byType(Row),
      ));
      expect(row.mainAxisSize, equals(MainAxisSize.min));
    });

    testWidgets('should display inner circle with correct styling when selected', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Radio';
      const String value = 'test_value';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: title,
              value: value,
              isSelected: true,
            ),
          ),
        ),
      );

      // Find all containers
      final Finder containers = find.descendant(
        of: find.byType(EvoRadioWidget<String>),
        matching: find.byType(Container),
      );

      // Should have outer container and inner circle container
      expect(containers, findsNWidgets(2));

      // Get the inner container (child of the outer container)
      final Container outerContainer = tester.widget<Container>(containers.first);
      final Container innerContainer = outerContainer.child as Container;
      final BoxDecoration innerDecoration = innerContainer.decoration as BoxDecoration;

      // Assert - verify inner circle styling
      expect(innerDecoration.color, equals(evoColorsV2.backgroundPrimaryElement));
      expect(innerDecoration.shape, equals(BoxShape.circle));
    });
  });
}
