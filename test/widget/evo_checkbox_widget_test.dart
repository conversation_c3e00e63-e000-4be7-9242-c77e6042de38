import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';
import 'package:evoapp/widget/evo_checkbox_widget.dart';

void main() {
  setUpAll(() {
    getItRegisterColorV2();
    getItRegisterTextStyleV2();
  });

  tearDownAll(() {
    getIt.reset();
  });

  testWidgets('EvoCheckboxWidget displays title and responds to tap', (WidgetTester tester) async {
    bool tapped = false;
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoCheckboxWidget(
            value: false,
            title: 'Test Checkbox',
            onTap: () => tapped = true,
          ),
        ),
      ),
    );

    // Title is shown
    expect(find.text('Test Checkbox'), findsOneWidget);
    // Checkbox is unchecked (no check icon)
    expect(find.byType(SizedBox), findsWidgets);

    // Tap triggers callback
    await tester.tap(find.byType(EvoCheckboxWidget));
    expect(tapped, isTrue);
  });
}
